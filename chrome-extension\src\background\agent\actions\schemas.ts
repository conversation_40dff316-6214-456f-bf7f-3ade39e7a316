import { z } from 'zod';

export interface ActionSchema {
  name: string;
  description: string;
  schema: z.ZodType;
}

export const doneActionSchema: ActionSchema = {
  name: 'done',
  description: 'Complete task',
  schema: z.object({
    text: z.string(),
    success: z.boolean(),
  }),
};

// Basic Navigation Actions
export const searchGoogleActionSchema: ActionSchema = {
  name: 'search_google',
  description:
    'Search the query in Google in the current tab, the query should be a search query like humans search in Google, concrete and not vague or super long. More the single most important items.',
  schema: z.object({
    intent: z.string().default('').describe('purpose of this action'),
    query: z.string(),
  }),
};

export const goToUrlActionSchema: ActionSchema = {
  name: 'go_to_url',
  description: 'Navigate to URL in the current tab',
  schema: z.object({
    intent: z.string().default('').describe('purpose of this action'),
    url: z.string(),
  }),
};

export const goBackActionSchema: ActionSchema = {
  name: 'go_back',
  description: 'Go back to the previous page',
  schema: z.object({
    intent: z.string().default('').describe('purpose of this action'),
  }),
};

export const clickElementActionSchema: ActionSchema = {
  name: 'click_element',
  description: 'Click element by index',
  schema: z.object({
    intent: z.string().default('').describe('purpose of this action'),
    index: z.number().int().describe('index of the element'),
    xpath: z.string().nullable().optional().describe('xpath of the element'),
  }),
};

export const inputTextActionSchema: ActionSchema = {
  name: 'input_text',
  description: 'Input text into an interactive input element',
  schema: z.object({
    intent: z.string().default('').describe('purpose of this action'),
    index: z.number().int().describe('index of the element'),
    text: z.string().describe('text to input'),
    xpath: z.string().nullable().optional().describe('xpath of the element'),
  }),
};

// Tab Management Actions
export const switchTabActionSchema: ActionSchema = {
  name: 'switch_tab',
  description: 'Switch to tab by tab id',
  schema: z.object({
    intent: z.string().default('').describe('purpose of this action'),
    tab_id: z.number().int().describe('id of the tab to switch to'),
  }),
};

export const openTabActionSchema: ActionSchema = {
  name: 'open_tab',
  description: 'Open URL in new tab',
  schema: z.object({
    intent: z.string().default('').describe('purpose of this action'),
    url: z.string().describe('url to open'),
  }),
};

export const closeTabActionSchema: ActionSchema = {
  name: 'close_tab',
  description: 'Close tab by tab id',
  schema: z.object({
    intent: z.string().default('').describe('purpose of this action'),
    tab_id: z.number().int().describe('id of the tab'),
  }),
};

// Content Actions, not used currently
// export const extractContentActionSchema: ActionSchema = {
//   name: 'extract_content',
//   description:
//     'Extract page content to retrieve specific information from the page, e.g. all company names, a specific description, all information about, links with companies in structured format or simply links',
//   schema: z.object({
//     goal: z.string(),
//   }),
// };

// Cache Actions
export const cacheContentActionSchema: ActionSchema = {
  name: 'cache_content',
  description: 'Cache what you have found so far from the current page for future use',
  schema: z.object({
    intent: z.string().default('').describe('purpose of this action'),
    content: z.string().describe('content to cache'),
  }),
};

export const scrollToPercentActionSchema: ActionSchema = {
  name: 'scroll_to_percent',
  description:
    'Scrolls to a particular vertical percentage of the document or an element. If no index of element is specified, scroll the whole document.',
  schema: z.object({
    intent: z.string().default('').describe('purpose of this action'),
    yPercent: z.number().int().describe('percentage to scroll to - min 0, max 100; 0 is top, 100 is bottom'),
    index: z.number().int().nullable().optional().describe('index of the element'),
  }),
};

export const scrollToTopActionSchema: ActionSchema = {
  name: 'scroll_to_top',
  description: 'Scroll the document in the window or an element to the top',
  schema: z.object({
    intent: z.string().default('').describe('purpose of this action'),
    index: z.number().int().nullable().optional().describe('index of the element'),
  }),
};

export const scrollToBottomActionSchema: ActionSchema = {
  name: 'scroll_to_bottom',
  description: 'Scroll the document in the window or an element to the bottom',
  schema: z.object({
    intent: z.string().default('').describe('purpose of this action'),
    index: z.number().int().nullable().optional().describe('index of the element'),
  }),
};

export const previousPageActionSchema: ActionSchema = {
  name: 'previous_page',
  description:
    'Scroll the document in the window or an element to the previous page. If no index is specified, scroll the whole document.',
  schema: z.object({
    intent: z.string().default('').describe('purpose of this action'),
    index: z.number().int().nullable().optional().describe('index of the element'),
  }),
};

export const nextPageActionSchema: ActionSchema = {
  name: 'next_page',
  description:
    'Scroll the document in the window or an element to the next page. If no index is specified, scroll the whole document.',
  schema: z.object({
    intent: z.string().default('').describe('purpose of this action'),
    index: z.number().int().nullable().optional().describe('index of the element'),
  }),
};

export const scrollToTextActionSchema: ActionSchema = {
  name: 'scroll_to_text',
  description: 'If you dont find something which you want to interact with in current viewport, try to scroll to it',
  schema: z.object({
    intent: z.string().default('').describe('purpose of this action'),
    text: z.string().describe('text to scroll to'),
    nth: z
      .number()
      .int()
      .min(1)
      .default(1)
      .describe('which occurrence of the text to scroll to (1-indexed, default: 1)'),
  }),
};

export const sendKeysActionSchema: ActionSchema = {
  name: 'send_keys',
  description:
    'Send strings of special keys like Backspace, Insert, PageDown, Delete, Enter. Shortcuts such as `Control+o`, `Control+Shift+T` are supported as well. This gets used in keyboard press. Be aware of different operating systems and their shortcuts',
  schema: z.object({
    intent: z.string().default('').describe('purpose of this action'),
    keys: z.string().describe('keys to send'),
  }),
};

export const getDropdownOptionsActionSchema: ActionSchema = {
  name: 'get_dropdown_options',
  description: 'Get all options from a native dropdown',
  schema: z.object({
    intent: z.string().default('').describe('purpose of this action'),
    index: z.number().int().describe('index of the dropdown element'),
  }),
};

export const selectDropdownOptionActionSchema: ActionSchema = {
  name: 'select_dropdown_option',
  description: 'Select dropdown option for interactive element index by the text of the option you want to select',
  schema: z.object({
    intent: z.string().default('').describe('purpose of this action'),
    index: z.number().int().describe('index of the dropdown element'),
    text: z.string().describe('text of the option'),
  }),
};

export const waitActionSchema: ActionSchema = {
  name: 'wait',
  description: 'Wait for x seconds default 3, do NOT use this action unless user asks to wait explicitly',
  schema: z.object({
    intent: z.string().default('').describe('purpose of this action'),
    seconds: z.number().int().default(3).describe('amount of seconds'),
  }),
};

export const exportDataActionSchema: ActionSchema = {
  name: 'export_data',
  description: 'Export previously extracted data to a file format (CSV, JSON, Excel). Only use this when user explicitly requests data export.',
  schema: z.object({
    intent: z.string().default('').describe('purpose of this action'),
    format: z.enum(['csv', 'json', 'excel']).describe('export format requested by user'),
    filename: z.string().optional().describe('custom filename if specified by user'),
  }),
};

// Enhanced Export Actions
export const advancedExportActionSchema: ActionSchema = {
  name: 'advanced_export',
  description: 'Export data with advanced formatting, charts, and professional styling. Use when user wants enhanced export with visualizations.',
  schema: z.object({
    intent: z.string().default('').describe('purpose of this action'),
    format: z.enum(['excel', 'pdf', 'dashboard']).describe('advanced export format'),
    filename: z.string().optional().describe('custom filename if specified'),
    include_charts: z.boolean().default(false).describe('whether to include charts and visualizations'),
    chart_types: z.array(z.enum(['bar', 'line', 'pie', 'scatter', 'table'])).optional().describe('types of charts to generate'),
    styling: z.enum(['basic', 'professional', 'corporate']).default('professional').describe('styling theme'),
    summary_analysis: z.boolean().default(true).describe('include AI-generated summary and insights'),
  }),
};

export const chartGenerationActionSchema: ActionSchema = {
  name: 'generate_chart',
  description: 'Generate interactive charts from extracted data using Chart.js, ECharts, or ApexCharts.',
  schema: z.object({
    intent: z.string().default('').describe('purpose of this action'),
    chart_type: z.enum(['bar', 'line', 'pie', 'doughnut', 'scatter', 'area', 'radar']).describe('type of chart to generate'),
    title: z.string().describe('chart title'),
    x_axis_field: z.string().optional().describe('field to use for x-axis'),
    y_axis_field: z.string().optional().describe('field to use for y-axis'),
    group_by_field: z.string().optional().describe('field to group data by'),
    color_scheme: z.enum(['default', 'blue', 'green', 'red', 'purple', 'orange']).default('default').describe('color scheme'),
    interactive: z.boolean().default(true).describe('make chart interactive with tooltips and zoom'),
  }),
};

export const pdfReportActionSchema: ActionSchema = {
  name: 'generate_pdf_report',
  description: 'Generate a professional PDF report with data, charts, and analysis.',
  schema: z.object({
    intent: z.string().default('').describe('purpose of this action'),
    title: z.string().describe('report title'),
    subtitle: z.string().optional().describe('report subtitle'),
    include_summary: z.boolean().default(true).describe('include executive summary'),
    include_charts: z.boolean().default(true).describe('include data visualizations'),
    include_raw_data: z.boolean().default(false).describe('include raw data tables'),
    template: z.enum(['business', 'technical', 'research', 'minimal']).default('business').describe('report template'),
    branding: z.object({
      company_name: z.string().optional(),
      logo_url: z.string().optional(),
      color_primary: z.string().optional(),
    }).optional().describe('custom branding options'),
  }),
};

export const aiExportAssistantActionSchema: ActionSchema = {
  name: 'ai_export_assistant',
  description: 'Use AI to intelligently analyze data and suggest the best export format and visualizations.',
  schema: z.object({
    intent: z.string().default('').describe('purpose of this action'),
    user_goal: z.string().describe('what the user wants to accomplish with the data'),
    data_context: z.string().optional().describe('context about the data source and meaning'),
    audience: z.enum(['technical', 'business', 'general', 'executive']).default('general').describe('target audience'),
    auto_execute: z.boolean().default(false).describe('automatically execute the best export suggestion'),
  }),
};

export const dashboardGenerationActionSchema: ActionSchema = {
  name: 'generate_dashboard',
  description: 'Create an interactive web dashboard with multiple charts, filters, and data tables.',
  schema: z.object({
    intent: z.string().default('').describe('purpose of this action'),
    title: z.string().describe('dashboard title'),
    layout: z.enum(['grid', 'sidebar', 'tabs', 'single_page']).default('grid').describe('dashboard layout'),
    include_filters: z.boolean().default(true).describe('include interactive filters'),
    include_search: z.boolean().default(true).describe('include search functionality'),
    chart_types: z.array(z.enum(['bar', 'line', 'pie', 'table', 'kpi', 'gauge'])).describe('chart types to include'),
    theme: z.enum(['light', 'dark', 'corporate', 'modern']).default('modern').describe('dashboard theme'),
    auto_refresh: z.boolean().default(false).describe('enable auto-refresh for live data'),
  }),
};

// Screenshot and Screen Recording Actions
export const takeScreenshotActionSchema: ActionSchema = {
  name: 'take_screenshot',
  description: 'Take a screenshot of the current viewport or full page.',
  schema: z.object({
    intent: z.string().default('').describe('purpose of this action'),
    type: z.enum(['viewport', 'fullpage']).default('viewport').describe('screenshot type - viewport for visible area only, fullpage for entire page'),
    filename: z.string().optional().describe('custom filename for the screenshot'),
  }),
};

export const startScreenRecordingActionSchema: ActionSchema = {
  name: 'start_screen_recording',
  description: 'Start recording the screen, window, or tab.',
  schema: z.object({
    intent: z.string().default('').describe('purpose of this action'),
    source: z.enum(['screen', 'window', 'tab']).default('screen').describe('what to record'),
    quality: z.enum(['low', 'medium', 'high']).default('medium').describe('recording quality'),
  }),
};

export const stopScreenRecordingActionSchema: ActionSchema = {
  name: 'stop_screen_recording',
  description: 'Stop the current screen recording and save the video file.',
  schema: z.object({
    intent: z.string().default('').describe('purpose of this action'),
    filename: z.string().optional().describe('custom filename for the recording'),
  }),
};

export const getRecordingStatusActionSchema: ActionSchema = {
  name: 'get_recording_status',
  description: 'Check if screen recording is currently active.',
  schema: z.object({
    intent: z.string().default('').describe('purpose of this action'),
  }),
};
