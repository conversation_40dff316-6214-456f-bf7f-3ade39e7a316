import { resolve } from 'node:path';
import { defineConfig, type PluginOption } from "vite";
import libAssetsPlugin from '@laynezh/vite-plugin-lib-assets';
import makeManifestPlugin from './utils/plugins/make-manifest-plugin';
import { watchPublicPlugin, watchRebuildPlugin } from '@extension/hmr';
import { isDev, isProduction, watchOption } from '@extension/vite-config';
import fs from 'node:fs';

const rootDir = resolve(__dirname);
const srcDir = resolve(rootDir, 'src');

const outDir = resolve(rootDir, '..', 'dist');

// Custom plugin to copy lib files from chrome-extension/lib to dist/lib
const copyLibFilesPlugin = (): PluginOption => ({
  name: 'copy-lib-files',
  writeBundle() {
    const libSrcDir = resolve(rootDir, 'lib');
    const libDestDir = resolve(outDir, 'lib');

    if (fs.existsSync(libSrcDir)) {
      // Ensure destination directory exists
      fs.mkdirSync(libDestDir, { recursive: true });

      // Copy all files from lib folder (only the 3 essential libraries)
      const files = fs.readdirSync(libSrcDir);
      files.forEach(file => {
        const srcFile = resolve(libSrcDir, file);
        const destFile = resolve(libDestDir, file);
        fs.copyFileSync(srcFile, destFile);
      });

      console.log(`Copied ${files.length} library files from chrome-extension/lib to dist/lib`);
      console.log(`Libraries copied: ${files.join(', ')}`);
    } else {
      console.warn('Warning: lib directory not found at', libSrcDir);
    }
  }
});
export default defineConfig({
  resolve: {
    alias: {
      '@root': rootDir,
      '@src': srcDir,
      '@assets': resolve(srcDir, 'assets'),
    },
    conditions: ['browser', 'module', 'import', 'default'],
    mainFields: ['browser', 'module', 'main']
  },
  server: {
    // Restrict CORS to only allow localhost
    cors: {
      origin: ['http://localhost:5173', 'http://localhost:3000'],
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      credentials: true
    },
    host: 'localhost',
    sourcemapIgnoreList: false,
  },
  plugins: [
    libAssetsPlugin({
      outputPath: outDir,
    }) as PluginOption,
    watchPublicPlugin(),
    makeManifestPlugin({ outDir }),
    copyLibFilesPlugin(),
    isDev && watchRebuildPlugin({ reload: true, id: 'chrome-extension-hmr' }),
  ],
  publicDir: resolve(rootDir, 'public'),
  build: {
    lib: {
      formats: ['iife'],
      entry: resolve(__dirname, 'src/background/index.ts'),
      name: 'BackgroundScript',
      fileName: 'background',
    },
    outDir,
    emptyOutDir: false,
    sourcemap: isDev,
    minify: isProduction,
    reportCompressedSize: isProduction,
    watch: watchOption,
    rollupOptions: {
      external: [
        'chrome',
        // 'chromium-bidi/lib/cjs/bidiMapper/BidiMapper.js'
      ],
    },
  },

  define: {
    'import.meta.env.DEV': isDev,
  },

  envDir: '../',
});
